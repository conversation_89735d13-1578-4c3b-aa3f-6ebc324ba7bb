/**
 * Mega Menu JavaScript for DmrThema
 * Fare ile mega menu uzerinde gezinirken menunun kapanmasini onler
 */

document.addEventListener('DOMContentLoaded', function() {

    // Mega menu elementlerini sec
    const megaMenuItems = document.querySelectorAll('.main-navigation ul li.has-mega-menu');

    if (megaMenuItems.length === 0) {
        return; // Mega menu yoksa cik
    }
    
    // Her mega menu item icin event listener'lar ekle
    megaMenuItems.forEach(function(menuItem) {
        const subMenu = menuItem.querySelector('.sub-menu');

        if (!subMenu) {
            return; // Alt menu yoksa devam et
        }

        let hoverTimeout;

        // Menu item uzerine fare geldiginde
        menuItem.addEventListener('mouseenter', function() {
            // Timeout'u temizle (eger varsa)
            if (hoverTimeout) {
                clearTimeout(hoverTimeout);
                hoverTimeout = null;
            }

            // Diger acik mega menuleri kapat
            closeMegaMenus();

            // Bu mega menuyu ac - animasyon icin kisa gecikme
            requestAnimationFrame(function() {
                menuItem.classList.add('mega-menu-active');
                // 4 sütunlu mega menu için hover efektlerini başlat
                initializeMegaMenuHover(menuItem);
            });
        });
        
        // Menu item'dan fare ciktiginda
        menuItem.addEventListener('mouseleave', function() {
            // Kisa bir gecikme ile menu kapat
            // Bu gecikme fareyi mega menu uzerine tasima firsati verir
            hoverTimeout = setTimeout(function() {
                menuItem.classList.remove('mega-menu-active');
            }, 150); // 150ms gecikme
        });
        
        // Sub menu uzerine fare geldiginde
        subMenu.addEventListener('mouseenter', function() {
            // Timeout'u temizle - menu acik kalsin
            if (hoverTimeout) {
                clearTimeout(hoverTimeout);
                hoverTimeout = null;
            }
            
            // Menu acik kalsin
            menuItem.classList.add('mega-menu-active');
        });
        
        // Sub menu'dan fare ciktiginda
        subMenu.addEventListener('mouseleave', function() {
            // Menu kapat
            menuItem.classList.remove('mega-menu-active');
        });
    });
    
    // Tum mega menuleri kapat
    function closeMegaMenus() {
        megaMenuItems.forEach(function(item) {
            item.classList.remove('mega-menu-active');
        });
    }
    
    // Sayfa uzerinde baska bir yere tiklandiginda mega menuleri kapat
    document.addEventListener('click', function(e) {
        // Tiklanilan element mega menu icinde degilse
        let isInsideMegaMenu = false;
        
        megaMenuItems.forEach(function(menuItem) {
            if (menuItem.contains(e.target)) {
                isInsideMegaMenu = true;
            }
        });
        
        if (!isInsideMegaMenu) {
            closeMegaMenus();
        }
    });
    
    // ESC tusuna basildiginda mega menuleri kapat
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeMegaMenus();
        }
    });

    // 4 sütunlu mega menu hover efektlerini başlat
    function initializeMegaMenuHover(menuItem) {
        const subMenuContainer = menuItem.querySelector('.sub-menu-container');
        if (!subMenuContainer) return;

        // Bloklu mega menu kontrolü - sayfa içeriği varsa grid yapısını uygulama
        const hasPageContent = subMenuContainer.querySelector('.mega-menu-page-content');
        if (hasPageContent) {
            // Bloklu mega menu - hiçbir şey yapma, mevcut yapıyı koru
            return;
        }

        // Mobil kontrolü
        const isMobile = window.innerWidth <= 768;
        const isTablet = window.innerWidth <= 1024 && window.innerWidth > 768;

        // Sadece bloksuz mega menüler için grid yapıya dönüştür
        convertToFourColumnLayout(subMenuContainer);

        // Mobilde farklı davranış
        if (isMobile) {
            // Mobilde tüm sütunları göster, hover efekti yok
            showAllColumnsForMobile(subMenuContainer);
            return;
        }

        // İlk sütundaki ana menü öğelerine hover efekti ekle
        const column1 = subMenuContainer.querySelector('.mega-menu-column-1');
        if (!column1) return;

        const mainMenuItems = column1.querySelectorAll('li > a');

        mainMenuItems.forEach(function(mainItem, index) {
            // Hover efekti için debounce
            let hoverTimer;

            mainItem.addEventListener('mouseenter', function() {
                // Mobil kontrolü tekrar yap
                if (window.innerWidth <= 768) return;

                // Önceki timer'ı temizle
                if (hoverTimer) {
                    clearTimeout(hoverTimer);
                }

                // Kısa gecikme ile hover efektini uygula
                hoverTimer = setTimeout(() => {
                    // Tüm ana menü öğelerinden active sınıfını kaldır
                    mainMenuItems.forEach(item => item.classList.remove('active'));

                    // Bu öğeye active sınıfı ekle
                    this.classList.add('active');

                    // 2-4. sütunları gizle
                    hideAllSubColumns(subMenuContainer);

                    // Bu menü öğesinin alt kategorilerini göster
                    showSubCategoriesForItem(subMenuContainer, this, index, isTablet);
                }, 100); // 100ms gecikme
            });

            // Mouse leave olayı
            mainItem.addEventListener('mouseleave', function() {
                if (hoverTimer) {
                    clearTimeout(hoverTimer);
                }
            });
        });

        // İlk öğeyi varsayılan olarak aktif yap (sadece desktop'ta)
        if (mainMenuItems.length > 0 && !isMobile) {
            setTimeout(() => {
                mainMenuItems[0].classList.add('active');
                showSubCategoriesForItem(subMenuContainer, mainMenuItems[0], 0, isTablet);
            }, 50); // Kısa gecikme ile ilk öğeyi aktif yap
        }
    }

    // Mevcut yapıyı 4 sütunlu yapıya dönüştür
    function convertToFourColumnLayout(container) {
        // Eğer zaten dönüştürülmüşse tekrar yapma
        if (container.querySelector('.mega-menu-column-1')) return;

        // Mevcut li öğelerini al
        const existingItems = Array.from(container.children);

        // Container'ı temizle
        container.innerHTML = '';

        // 4 sütun oluştur
        const column1 = document.createElement('div');
        column1.className = 'mega-menu-column-1';

        const column2 = document.createElement('div');
        column2.className = 'mega-menu-column-2';

        const column3 = document.createElement('div');
        column3.className = 'mega-menu-column-3';

        const column4 = document.createElement('div');
        column4.className = 'mega-menu-column-4';

        // Mevcut öğeleri ilk sütuna taşı
        existingItems.forEach(item => {
            column1.appendChild(item);
        });

        // Sütunları container'a ekle
        container.appendChild(column1);
        container.appendChild(column2);
        container.appendChild(column3);
        container.appendChild(column4);
    }

    // Tüm alt sütunları gizle
    function hideAllSubColumns(container) {
        const columns = container.querySelectorAll('.mega-menu-column-2, .mega-menu-column-3, .mega-menu-column-4');
        columns.forEach(column => {
            column.classList.remove('active');
            column.innerHTML = '';
        });
    }

    // Belirli bir menü öğesi için alt kategorileri göster
    function showSubCategoriesForItem(container, menuItem, index, isTablet = false) {
        const parentLi = menuItem.closest('li');
        const subMenuUl = parentLi.querySelector('ul');

        if (!subMenuUl) return;

        // Alt menü öğelerini al
        const subItems = Array.from(subMenuUl.children);

        const column2 = container.querySelector('.mega-menu-column-2');
        const column3 = container.querySelector('.mega-menu-column-3');
        const column4 = container.querySelector('.mega-menu-column-4');

        // Sütunları temizle
        column2.innerHTML = '';
        column3.innerHTML = '';
        column4.innerHTML = '';

        if (isTablet) {
            // Tablet için sadece 2. sütunu kullan
            const ul = document.createElement('ul');
            subItems.forEach(item => {
                ul.appendChild(item.cloneNode(true));
            });
            column2.appendChild(ul);
            column2.classList.add('active');
        } else {
            // Desktop için 3 sütuna böl
            const itemsPerColumn = Math.ceil(subItems.length / 3);

            // Alt öğeleri sütunlara dağıt
            subItems.forEach((item, itemIndex) => {
                const clonedItem = item.cloneNode(true);

                if (itemIndex < itemsPerColumn) {
                    const ul = column2.querySelector('ul') || document.createElement('ul');
                    if (!column2.querySelector('ul')) column2.appendChild(ul);
                    ul.appendChild(clonedItem);
                } else if (itemIndex < itemsPerColumn * 2) {
                    const ul = column3.querySelector('ul') || document.createElement('ul');
                    if (!column3.querySelector('ul')) column3.appendChild(ul);
                    ul.appendChild(clonedItem);
                } else {
                    const ul = column4.querySelector('ul') || document.createElement('ul');
                    if (!column4.querySelector('ul')) column4.appendChild(ul);
                    ul.appendChild(clonedItem);
                }
            });

            // Sütunları göster
            if (column2.querySelector('ul')) column2.classList.add('active');
            if (column3.querySelector('ul')) column3.classList.add('active');
            if (column4.querySelector('ul')) column4.classList.add('active');
        }
    }

    // Mobil için tüm sütunları göster
    function showAllColumnsForMobile(container) {
        const column1 = container.querySelector('.mega-menu-column-1');
        if (!column1) return;

        const mainMenuItems = column1.querySelectorAll('> li');

        mainMenuItems.forEach((mainItem, index) => {
            const subMenuUl = mainItem.querySelector('ul');
            if (!subMenuUl) return;

            // Alt menü öğelerini mobil için düzenle
            const subItems = Array.from(subMenuUl.children);
            const targetColumn = index % 3 === 0 ? '.mega-menu-column-2' :
                               index % 3 === 1 ? '.mega-menu-column-3' : '.mega-menu-column-4';

            const targetColumnEl = container.querySelector(targetColumn);
            if (targetColumnEl) {
                const ul = targetColumnEl.querySelector('ul') || document.createElement('ul');
                if (!targetColumnEl.querySelector('ul')) {
                    targetColumnEl.appendChild(ul);
                }

                // Ana menü başlığı ekle
                const titleLi = document.createElement('li');
                const titleA = document.createElement('a');
                titleA.textContent = mainItem.querySelector('> a').textContent;
                titleA.style.fontWeight = 'bold';
                titleA.style.color = '#333';
                titleA.style.borderBottom = '1px solid #e0e0e0';
                titleA.style.marginBottom = '10px';
                titleA.style.display = 'block';
                titleLi.appendChild(titleA);
                ul.appendChild(titleLi);

                // Alt öğeleri ekle
                subItems.forEach(item => {
                    ul.appendChild(item.cloneNode(true));
                });

                targetColumnEl.classList.add('active');
            }
        });
    }

    // Pencere boyutu değiştiğinde mega menüyü yeniden düzenle
    window.addEventListener('resize', function() {
        const activeMegaMenu = document.querySelector('.main-navigation ul li.mega-menu-active');
        if (activeMegaMenu) {
            const subMenuContainer = activeMegaMenu.querySelector('.sub-menu-container');
            // Sadece bloksuz mega menüler için yeniden düzenle
            if (subMenuContainer && !subMenuContainer.querySelector('.mega-menu-page-content')) {
                // Kısa bir gecikme ile yeniden düzenle
                setTimeout(() => {
                    initializeMegaMenuHover(activeMegaMenu);
                }, 100);
            }
        }
    });

});
